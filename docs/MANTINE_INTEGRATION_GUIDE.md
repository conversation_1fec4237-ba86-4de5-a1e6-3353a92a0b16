# Mantine UI 集成指南

## 概述

本指南详细介绍了如何在硅基茶馆2077项目中使用 Mantine UI 组件库。Mantine UI 已成功集成并配置了赛博朋克风格主题，与现有的微信公众号文章组件库完美兼容。

## 🎨 主题配置

### 赛博朋克风格主题

项目采用自定义的赛博朋克主题，主要特色：

- **主色调**: 青色(cyan) 和 粉色(pink) 渐变
- **背景**: 深色主题，使用 slate-900/slate-800 色调
- **特效**: 毛玻璃效果、发光阴影、悬浮动画
- **字体**: 与现有项目保持一致

### 主题代码示例

```typescript
const cyberpunkTheme = createTheme({
  primaryColor: 'cyan',
  defaultColorScheme: 'dark',
  colors: {
    cyan: ['#cffafe', '#a5f3fc', '#67e8f9', '#22d3ee', '#06b6d4', ...],
    pink: ['#fdf2f8', '#fce7f3', '#fbcfe8', '#f9a8d4', '#f472b6', ...],
  },
  components: {
    Button: {
      styles: {
        root: {
          background: 'linear-gradient(45deg, var(--cyber-cyan), var(--cyber-pink))',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(6, 182, 212, 0.4)',
          },
        },
      },
    },
  },
});
```

## 📦 已安装的包

### 核心包
- `@mantine/core@8.0.2` - 核心组件库
- `@mantine/hooks@8.0.2` - React hooks 工具集
- `@tabler/icons-react@3.33.0` - 图标库

### 扩展包
- `@mantine/form@8.0.2` - 表单处理
- `@mantine/notifications@8.0.2` - 通知系统

## 🚀 基础使用

### 导入组件

```typescript
import { 
  Button, 
  Card, 
  Text, 
  Group, 
  Stack,
  TextInput,
  Select 
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconCode } from '@tabler/icons-react';
```

### 基础组件示例

```typescript
// 赛博朋克风格按钮
<Button 
  variant="gradient" 
  gradient={{ from: 'cyan', to: 'pink' }}
  leftSection={<IconCode size={16} />}
>
  硅基茶馆2077
</Button>

// 卡片组件
<Card shadow="sm" padding="lg" radius="md" withBorder>
  <Text fw={500} size="lg" className="text-cyan-400">
    标题
  </Text>
  <Text size="sm" c="dimmed">
    内容描述
  </Text>
</Card>

// 通知系统
notifications.show({
  title: '🚀 操作成功',
  message: '欢迎来到硅基茶馆2077！',
  color: 'cyan',
  icon: <IconCheck size={16} />,
});
```

## 🔗 与现有组件的兼容性

### 完美兼容

Mantine UI 与现有的微信文章组件库完全兼容：

```typescript
// 可以在同一页面中混合使用
import { Button, Card } from '@mantine/core';
import { ImageContainer, HighlightBox, CodeBlock } from '../posts/components';

export default function HybridPage() {
  return (
    <div>
      {/* Mantine 组件 */}
      <Card>
        <Button variant="gradient">现代化按钮</Button>
      </Card>
      
      {/* 现有微信组件 */}
      <ImageContainer src="/image.jpg" alt="示例" />
      <HighlightBox type="info">重要提示</HighlightBox>
      <CodeBlock language="typescript">代码示例</CodeBlock>
    </div>
  );
}
```

### 样式隔离

- Mantine 组件使用自己的 CSS 类名空间
- 现有组件继续使用 Tailwind CSS
- 两套样式系统互不干扰

## 📱 移动端优化

### 响应式设计

```typescript
// 使用 Mantine 的响应式系统
<Grid>
  <Grid.Col span={{ base: 12, md: 6 }}>
    <Card>移动端全宽，桌面端半宽</Card>
  </Grid.Col>
</Grid>

// 响应式容器
<Container size="lg">
  <Stack gap={{ base: 'md', md: 'xl' }}>
    内容区域
  </Stack>
</Container>
```

### 微信环境适配

- 所有组件经过微信浏览器测试
- 触摸交互优化
- 字体大小适配移动端阅读

## 🎯 最佳实践

### 1. 组件选择策略

- **新功能**: 优先使用 Mantine 组件
- **文章内容**: 继续使用现有微信组件
- **管理界面**: 推荐使用 Mantine 组件

### 2. 主题一致性

```typescript
// 保持赛博朋克风格
<Button 
  variant="gradient" 
  gradient={{ from: 'cyan', to: 'pink' }}
>
  按钮文字
</Button>

// 使用项目色彩
<Text className="text-cyan-400">青色文字</Text>
<Text className="text-pink-400">粉色文字</Text>
```

### 3. 性能优化

```typescript
// 按需导入组件
import { Button } from '@mantine/core';

// 避免导入整个库
// ❌ import * as Mantine from '@mantine/core';
```

## 🔧 常见问题

### Q: Mantine 组件样式被 Tailwind 覆盖？
A: 项目已配置样式优先级，Mantine 组件样式会正确显示。

### Q: 如何自定义 Mantine 组件样式？
A: 使用 `className` 属性或 Mantine 的 `styles` 属性：

```typescript
<Button 
  className="custom-button"
  styles={{
    root: { backgroundColor: 'var(--cyber-cyan)' }
  }}
>
  自定义按钮
</Button>
```

### Q: 移动端显示异常？
A: 确保使用 Mantine 的响应式属性，如 `span={{ base: 12, md: 6 }}`。

## 📚 示例页面

- `/mantine-test` - 基础组件展示
- `/mantine-integration` - 兼容性验证
- 查看源码了解具体实现

## 🔮 未来扩展

可以根据需要添加更多 Mantine 包：

```bash
# 日期选择器
pnpm add @mantine/dates dayjs

# 图表组件
pnpm add @mantine/charts recharts

# 搜索组件
pnpm add @mantine/spotlight
```

## 📞 技术支持

如遇到问题，请参考：
1. [Mantine 官方文档](https://mantine.dev/)
2. 项目内的示例页面
3. 现有组件库文档 (`docs/COMPONENT_LIBRARY_GUIDE.md`)

---

**硅基茶馆2077 × Mantine UI - 探索未来科技的边界** 🚀
