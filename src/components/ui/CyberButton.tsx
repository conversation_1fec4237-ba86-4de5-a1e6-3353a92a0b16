import { Button, ButtonProps } from '@mantine/core';
import { forwardRef } from 'react';

interface CyberButtonProps extends ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  cyber?: boolean;
}

/**
 * 赛博朋克风格按钮组件
 * 基于 Mantine Button，添加硅基茶馆2077主题样式
 */
export const CyberButton = forwardRef<HTMLButtonElement, CyberButtonProps>(
  ({ variant = 'primary', cyber = true, className, children, ...props }, ref) => {
    const getVariantStyles = () => {
      const baseStyles = cyber ? 'transition-all duration-300 hover:transform hover:-translate-y-0.5' : '';
      
      switch (variant) {
        case 'primary':
          return `${baseStyles} bg-gradient-to-r from-cyan-500 to-pink-500 hover:from-cyan-400 hover:to-pink-400 border-0 text-white font-medium ${cyber ? 'hover:shadow-lg hover:shadow-cyan-500/25' : ''}`;
        
        case 'secondary':
          return `${baseStyles} bg-gradient-to-r from-slate-700 to-slate-600 hover:from-slate-600 hover:to-slate-500 border-0 text-white font-medium ${cyber ? 'hover:shadow-lg hover:shadow-slate-500/25' : ''}`;
        
        case 'outline':
          return `${baseStyles} bg-transparent border-2 border-cyan-500 text-cyan-400 hover:bg-cyan-500/10 hover:text-cyan-300 font-medium ${cyber ? 'hover:shadow-lg hover:shadow-cyan-500/25' : ''}`;
        
        case 'ghost':
          return `${baseStyles} bg-transparent border-0 text-slate-300 hover:bg-slate-800/50 hover:text-white font-medium`;
        
        case 'danger':
          return `${baseStyles} bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-500 hover:to-pink-500 border-0 text-white font-medium ${cyber ? 'hover:shadow-lg hover:shadow-red-500/25' : ''}`;
        
        default:
          return baseStyles;
      }
    };

    const mantineVariant = variant === 'outline' ? 'outline' : 'filled';
    const mantineColor = variant === 'primary' ? 'cyan' : 
                        variant === 'danger' ? 'red' : 
                        variant === 'outline' ? 'cyan' : 'gray';

    return (
      <Button
        ref={ref}
        variant={mantineVariant}
        color={mantineColor}
        className={`${getVariantStyles()} ${className || ''}`}
        {...props}
      >
        {children}
      </Button>
    );
  }
);

CyberButton.displayName = 'CyberButton';

// 预设的常用按钮变体
export const PrimaryButton = (props: Omit<CyberButtonProps, 'variant'>) => (
  <CyberButton variant="primary" {...props} />
);

export const SecondaryButton = (props: Omit<CyberButtonProps, 'variant'>) => (
  <CyberButton variant="secondary" {...props} />
);

export const OutlineButton = (props: Omit<CyberButtonProps, 'variant'>) => (
  <CyberButton variant="outline" {...props} />
);

export const GhostButton = (props: Omit<CyberButtonProps, 'variant'>) => (
  <CyberButton variant="ghost" {...props} />
);

export const DangerButton = (props: Omit<CyberButtonProps, 'variant'>) => (
  <CyberButton variant="danger" {...props} />
);
