'use client';

import { ActionIcon, Tooltip, SegmentedControl, Group, Text } from '@mantine/core';
import { IconSun, IconMoon, IconDeviceDesktop } from '@tabler/icons-react';
import { useTheme } from '@/contexts/ThemeContext';
import { ThemeMode } from '@/lib/theme';

interface ThemeToggleProps {
  variant?: 'icon' | 'segmented';
  size?: 'xs' | 'sm' | 'md' | 'lg';
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  variant = 'icon', 
  size = 'md' 
}) => {
  const { themeMode, setThemeMode, colorScheme } = useTheme();

  if (variant === 'segmented') {
    return (
      <SegmentedControl
        size={size}
        value={themeMode}
        onChange={(value) => setThemeMode(value as ThemeMode)}
        data={[
          {
            value: 'light',
            label: (
              <Group gap="xs" justify="center">
                <IconSun size={16} />
                <Text size="sm">明亮</Text>
              </Group>
            ),
          },
          {
            value: 'dark',
            label: (
              <Group gap="xs" justify="center">
                <IconMoon size={16} />
                <Text size="sm">深色</Text>
              </Group>
            ),
          },
          {
            value: 'auto',
            label: (
              <Group gap="xs" justify="center">
                <IconDeviceDesktop size={16} />
                <Text size="sm">自动</Text>
              </Group>
            ),
          },
        ]}
        styles={{
          root: {
            backgroundColor: colorScheme === 'dark' 
              ? 'rgba(15, 23, 42, 0.8)' 
              : 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(10px)',
            border: colorScheme === 'dark'
              ? '1px solid rgba(71, 85, 105, 0.3)'
              : '1px solid rgba(6, 182, 212, 0.2)',
          },
          control: {
            border: 'none',
            '&[data-active]': {
              background: 'linear-gradient(45deg, #06b6d4, #ec4899)',
              color: 'white',
            },
          },
        }}
      />
    );
  }

  const getIcon = () => {
    switch (themeMode) {
      case 'light':
        return <IconSun size={18} />;
      case 'dark':
        return <IconMoon size={18} />;
      case 'auto':
        return <IconDeviceDesktop size={18} />;
      default:
        return <IconMoon size={18} />;
    }
  };

  const getTooltipText = () => {
    switch (themeMode) {
      case 'light':
        return '当前：明亮模式';
      case 'dark':
        return '当前：深色模式';
      case 'auto':
        return '当前：跟随系统';
      default:
        return '切换主题';
    }
  };

  const handleClick = () => {
    const modes: ThemeMode[] = ['dark', 'light', 'auto'];
    const currentIndex = modes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  };

  return (
    <Tooltip label={getTooltipText()} position="bottom">
      <ActionIcon
        variant="subtle"
        size={size}
        onClick={handleClick}
        className={`
          transition-all duration-300 hover:transform hover:-translate-y-0.5
          ${colorScheme === 'dark' 
            ? 'text-cyan-400 hover:bg-cyan-500/10 hover:text-cyan-300' 
            : 'text-cyan-600 hover:bg-cyan-500/10 hover:text-cyan-700'
          }
        `}
        styles={{
          root: {
            '&:hover': {
              boxShadow: colorScheme === 'dark' 
                ? '0 4px 15px rgba(6, 182, 212, 0.3)'
                : '0 4px 15px rgba(6, 182, 212, 0.2)',
            },
          },
        }}
      >
        {getIcon()}
      </ActionIcon>
    </Tooltip>
  );
};
