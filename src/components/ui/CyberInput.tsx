import { TextInput, TextInputProps, Textarea, TextareaProps, Select, SelectProps } from '@mantine/core';
import { forwardRef } from 'react';

interface CyberInputProps extends TextInputProps {
  cyber?: boolean;
}

interface CyberTextareaProps extends TextareaProps {
  cyber?: boolean;
}

interface CyberSelectProps extends SelectProps {
  cyber?: boolean;
}

/**
 * 赛博朋克风格文本输入组件
 */
export const CyberInput = forwardRef<HTMLInputElement, CyberInputProps>(
  ({ cyber = true, className, ...props }, ref) => {
    const cyberStyles = cyber 
      ? 'bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 focus:border-cyan-500 focus:ring-cyan-500/20 transition-all duration-300 backdrop-blur-sm'
      : '';

    return (
      <TextInput
        ref={ref}
        className={`${cyberStyles} ${className || ''}`}
        styles={{
          input: {
            backgroundColor: 'rgba(15, 23, 42, 0.5)',
            borderColor: 'rgba(71, 85, 105, 0.6)',
            color: 'white',
            backdropFilter: 'blur(10px)',
            '&::placeholder': {
              color: 'rgba(148, 163, 184, 0.7)',
            },
            '&:focus': {
              borderColor: '#06b6d4',
              boxShadow: '0 0 0 1px rgba(6, 182, 212, 0.2)',
            },
          },
          label: {
            color: 'rgba(148, 163, 184, 0.9)',
            fontWeight: 500,
          },
        }}
        {...props}
      />
    );
  }
);

CyberInput.displayName = 'CyberInput';

/**
 * 赛博朋克风格文本域组件
 */
export const CyberTextarea = forwardRef<HTMLTextAreaElement, CyberTextareaProps>(
  ({ cyber = true, className, ...props }, ref) => {
    const cyberStyles = cyber 
      ? 'bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 focus:border-cyan-500 focus:ring-cyan-500/20 transition-all duration-300 backdrop-blur-sm'
      : '';

    return (
      <Textarea
        ref={ref}
        className={`${cyberStyles} ${className || ''}`}
        styles={{
          input: {
            backgroundColor: 'rgba(15, 23, 42, 0.5)',
            borderColor: 'rgba(71, 85, 105, 0.6)',
            color: 'white',
            backdropFilter: 'blur(10px)',
            minHeight: '120px',
            '&::placeholder': {
              color: 'rgba(148, 163, 184, 0.7)',
            },
            '&:focus': {
              borderColor: '#06b6d4',
              boxShadow: '0 0 0 1px rgba(6, 182, 212, 0.2)',
            },
          },
          label: {
            color: 'rgba(148, 163, 184, 0.9)',
            fontWeight: 500,
          },
        }}
        {...props}
      />
    );
  }
);

CyberTextarea.displayName = 'CyberTextarea';

/**
 * 赛博朋克风格选择器组件
 */
export const CyberSelect = forwardRef<HTMLInputElement, CyberSelectProps>(
  ({ cyber = true, className, ...props }, ref) => {
    const cyberStyles = cyber 
      ? 'bg-slate-900/50 border-slate-600 text-white transition-all duration-300 backdrop-blur-sm'
      : '';

    return (
      <Select
        ref={ref}
        className={`${cyberStyles} ${className || ''}`}
        styles={{
          input: {
            backgroundColor: 'rgba(15, 23, 42, 0.5)',
            borderColor: 'rgba(71, 85, 105, 0.6)',
            color: 'white',
            backdropFilter: 'blur(10px)',
            '&:focus': {
              borderColor: '#06b6d4',
              boxShadow: '0 0 0 1px rgba(6, 182, 212, 0.2)',
            },
          },
          label: {
            color: 'rgba(148, 163, 184, 0.9)',
            fontWeight: 500,
          },
          dropdown: {
            backgroundColor: 'rgba(15, 23, 42, 0.95)',
            borderColor: 'rgba(71, 85, 105, 0.6)',
            backdropFilter: 'blur(20px)',
          },
          option: {
            color: 'white',
            '&[data-selected]': {
              backgroundColor: 'rgba(6, 182, 212, 0.2)',
              color: '#06b6d4',
            },
            '&[data-hovered]': {
              backgroundColor: 'rgba(71, 85, 105, 0.3)',
            },
          },
        }}
        {...props}
      />
    );
  }
);

CyberSelect.displayName = 'CyberSelect';

// 导出所有组件
export { CyberInput as default };
