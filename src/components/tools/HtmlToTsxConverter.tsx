'use client';

import { useState, useCallback } from 'react';
import { convertHtmlToTsx, validateTsx } from '@/lib/htmlToTsx';
import {
  Container,
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Badge,
  Alert,
  Code,
  List
} from '@mantine/core';
import { IconCheck, IconX, IconRefresh, IconCopy, IconFileText, IconSettings } from '@tabler/icons-react';
import { CyberButton, CyberTextarea } from '@/components/ui';

export default function HtmlToTsxConverter() {
  const [htmlInput, setHtmlInput] = useState('');
  const [tsxOutput, setTsxOutput] = useState('');
  const [validationResult, setValidationResult] = useState<{ isValid: boolean; errors: string[] }>({ isValid: true, errors: [] });
  const [copySuccess, setCopySuccess] = useState(false);

  // 转换函数
  const handleConvert = useCallback(() => {
    if (!htmlInput.trim()) {
      setTsxOutput('');
      setValidationResult({ isValid: true, errors: [] });
      return;
    }

    try {
      const converted = convertHtmlToTsx(htmlInput);
      setTsxOutput(converted);
      
      // 验证转换结果
      const validation = validateTsx(converted);
      setValidationResult(validation);
    } catch (error) {
      setTsxOutput('');
      setValidationResult({ 
        isValid: false, 
        errors: [`转换错误: ${error instanceof Error ? error.message : '未知错误'}`] 
      });
    }
  }, [htmlInput]);

  // 复制到剪贴板
  const handleCopy = useCallback(async () => {
    if (!tsxOutput) return;

    try {
      await navigator.clipboard.writeText(tsxOutput);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, [tsxOutput]);

  // 清空输入
  const handleClear = useCallback(() => {
    setHtmlInput('');
    setTsxOutput('');
    setValidationResult({ isValid: true, errors: [] });
  }, []);

  // 示例 HTML
  const loadExample = useCallback(() => {
    const exampleHtml = `<div class="container">
  <h1 style="color: red; font-size: 24px;">标题</h1>
  <p>这是一个段落</p>
  <img src="image.jpg" alt="图片" width="300">
  <input type="text" placeholder="输入文本" disabled>
  <button onclick="handleClick()">点击按钮</button>
  <label for="email">邮箱:</label>
  <input type="email" id="email" required>
</div>`;
    setHtmlInput(exampleHtml);
  }, []);

  return (
    <Container size="xl" py="xl">
      <Paper
        p="xl"
        radius="lg"
        className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50"
        mb="xl"
      >
        <Stack gap="md">
          <Group justify="space-between" align="flex-start">
            <Stack gap="xs">
              <Title
                order={1}
                size="2rem"
                className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400"
              >
                HTML 到 React TSX 转换器
              </Title>
              <Text c="dimmed" size="lg">
                将原生 HTML 代码转换为 React TSX 语法，支持属性名转换、自闭合标签、内联样式对象化等功能
              </Text>
            </Stack>
            <Badge
              variant="gradient"
              gradient={{ from: 'cyan', to: 'pink' }}
              size="lg"
            >
              v2.0
            </Badge>
          </Group>

          <Group gap="md">
            <CyberButton
              variant="secondary"
              leftSection={<IconFileText size={16} />}
              onClick={loadExample}
            >
              加载示例
            </CyberButton>
            <CyberButton
              variant="ghost"
              leftSection={<IconX size={16} />}
              onClick={handleClear}
            >
              清空
            </CyberButton>
          </Group>
        </Stack>
      </Paper>

      <Grid>
        {/* HTML 输入区域 */}
        <Grid.Col span={{ base: 12, lg: 6 }}>
          <Paper
            p="lg"
            radius="lg"
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50"
            h="100%"
          >
            <Stack gap="md" h="100%">
              <Group justify="space-between" align="center">
                <Title order={3} size="1.2rem" className="text-white">
                  HTML 输入
                </Title>
                <Badge variant="light" color="cyan" size="sm">
                  {htmlInput.length} 字符
                </Badge>
              </Group>

              <CyberTextarea
                value={htmlInput}
                onChange={(e) => setHtmlInput(e.target.value)}
                onInput={handleConvert}
                placeholder="在此粘贴您的 HTML 代码..."
                minRows={16}
                autosize
                styles={{
                  input: {
                    fontFamily: 'monospace',
                    fontSize: '14px',
                  }
                }}
              />

              <Group gap="md">
                <CyberButton
                  variant="primary"
                  leftSection={<IconRefresh size={16} />}
                  onClick={handleConvert}
                  disabled={!htmlInput.trim()}
                >
                  转换
                </CyberButton>
              </Group>
            </Stack>
          </Paper>
        </Grid.Col>

        {/* TSX 输出区域 */}
        <Grid.Col span={{ base: 12, lg: 6 }}>
          <Paper
            p="lg"
            radius="lg"
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50"
            h="100%"
          >
            <Stack gap="md" h="100%">
              <Group justify="space-between" align="center">
                <Title order={3} size="1.2rem" className="text-white">
                  TSX 输出
                </Title>
                <Group gap="xs">
                  {tsxOutput && (
                    <Badge variant="light" color="cyan" size="sm">
                      {tsxOutput.length} 字符
                    </Badge>
                  )}
                  {validationResult.isValid ? (
                    <Badge color="green" variant="light" size="sm">
                      <IconCheck size={12} style={{ marginRight: 4 }} />
                      有效
                    </Badge>
                  ) : (
                    <Badge color="red" variant="light" size="sm">
                      <IconX size={12} style={{ marginRight: 4 }} />
                      有错误
                    </Badge>
                  )}
                </Group>
              </Group>

              <div className="relative flex-1">
                <CyberTextarea
                  value={tsxOutput}
                  readOnly
                  placeholder="转换后的 TSX 代码将显示在这里..."
                  minRows={16}
                  autosize
                  styles={{
                    input: {
                      fontFamily: 'monospace',
                      fontSize: '14px',
                    }
                  }}
                />

                {tsxOutput && (
                  <div className="absolute top-3 right-3">
                    <CyberButton
                      size="xs"
                      variant={copySuccess ? "primary" : "ghost"}
                      onClick={handleCopy}
                    >
                      {copySuccess ? '已复制' : '复制'}
                    </CyberButton>
                  </div>
                )}
              </div>

              {tsxOutput && (
                <Group gap="md">
                  <CyberButton
                    variant="outline"
                    leftSection={<IconCopy size={16} />}
                    onClick={handleCopy}
                  >
                    复制到剪贴板
                  </CyberButton>
                </Group>
              )}
            </Stack>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* 验证错误显示 */}
      {!validationResult.isValid && validationResult.errors.length > 0 && (
        <Alert
          icon={<IconX size={16} />}
          title="验证错误"
          color="red"
          variant="light"
          mt="lg"
        >
          <List size="sm" spacing="xs">
            {validationResult.errors.map((error, index) => (
              <List.Item key={index}>{error}</List.Item>
            ))}
          </List>
        </Alert>
      )}

      {/* 功能说明 */}
      <Paper
        p="xl"
        radius="lg"
        className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/30"
        mt="xl"
      >
        <Stack gap="lg">
          <Group gap="xs">
            <IconSettings size={20} className="text-cyan-400" />
            <Title order={3} className="text-white">
              转换功能
            </Title>
          </Group>

          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Stack gap="sm">
                <Text fw={600} className="text-cyan-400">
                  属性名转换
                </Text>
                <List size="sm" spacing="xs">
                  <List.Item>
                    <Code>class</Code> → <Code>className</Code>
                  </List.Item>
                  <List.Item>
                    <Code>for</Code> → <Code>htmlFor</Code>
                  </List.Item>
                  <List.Item>
                    <Code>tabindex</Code> → <Code>tabIndex</Code>
                  </List.Item>
                  <List.Item>以及更多常用属性...</List.Item>
                </List>
              </Stack>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Stack gap="sm">
                <Text fw={600} className="text-cyan-400">
                  标签处理
                </Text>
                <List size="sm" spacing="xs">
                  <List.Item>
                    自闭合标签格式化 (<Code>&lt;img /&gt;</Code>)
                  </List.Item>
                  <List.Item>
                    事件处理器转换 (<Code>onclick</Code> → <Code>onClick</Code>)
                  </List.Item>
                  <List.Item>内联样式对象化</List.Item>
                  <List.Item>HTML 注释转 JSX 注释</List.Item>
                </List>
              </Stack>
            </Grid.Col>
          </Grid>
        </Stack>
      </Paper>
    </Container>
  );
}
