'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  AppShell,
  Group,
  Title,
  Burger,
  Drawer,
  Stack,
  UnstyledButton,
  Text,
  Container,
  Badge
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconArticle,
  IconPlus,
  IconTools,
  IconHome,
  IconRocket
} from '@tabler/icons-react';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { useTheme } from '@/contexts/ThemeContext';

const navItems = [
  { href: '/', label: '首页', icon: IconHome },
  { href: '/posts', label: '文章', icon: IconArticle },
  { href: '/posts/new', label: '新建', icon: IconPlus },
  { href: '/tools', label: '工具', icon: IconTools },
];

export default function Header() {
  const [opened, { toggle, close }] = useDisclosure(false);
  const pathname = usePathname();
  const { colorScheme } = useTheme();

  const NavLink = ({ href, label, icon: Icon, mobile = false }: {
    href: string;
    label: string;
    icon: any;
    mobile?: boolean;
  }) => {
    const isActive = pathname === href || (href !== '/' && pathname.startsWith(href));

    return (
      <UnstyledButton
        component={Link}
        href={href}
        onClick={mobile ? close : undefined}
        className={`
          px-3 py-2 rounded-lg transition-all duration-300 group
          ${isActive
            ? 'bg-gradient-to-r from-cyan-500/20 to-pink-500/20 text-cyan-400'
            : 'text-slate-300 hover:text-white hover:bg-slate-800/50'
          }
          ${mobile ? 'w-full' : ''}
        `}
      >
        <Group gap="xs" justify={mobile ? 'flex-start' : 'center'}>
          <Icon
            size={mobile ? 20 : 16}
            className={`transition-colors ${isActive ? 'text-cyan-400' : 'group-hover:text-cyan-400'}`}
          />
          <Text
            size={mobile ? 'md' : 'sm'}
            fw={isActive ? 600 : 500}
            className="transition-colors"
          >
            {label}
          </Text>
          {isActive && (
            <Badge size="xs" variant="gradient" gradient={{ from: 'cyan', to: 'pink' }}>
              •
            </Badge>
          )}
        </Group>
      </UnstyledButton>
    );
  };

  return (
    <>
      <header className="bg-slate-900/95 backdrop-blur-sm shadow-lg border-b border-slate-800/50 sticky top-0 z-50">
        <Container size="xl" py="md">
          <Group justify="space-between" align="center">
            {/* Logo */}
            <UnstyledButton component={Link} href="/" className="group">
              <Group gap="xs" align="center">
                <IconRocket
                  size={28}
                  className="text-cyan-400 group-hover:text-pink-400 transition-colors duration-300"
                />
                <Title
                  order={1}
                  size="1.5rem"
                  className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 group-hover:from-pink-400 group-hover:to-cyan-400 transition-all duration-300"
                >
                  硅基茶馆2077
                </Title>
              </Group>
            </UnstyledButton>

            {/* Desktop Navigation */}
            <Group gap="md" visibleFrom="sm">
              <Group gap="xs">
                {navItems.map((item) => (
                  <NavLink key={item.href} {...item} />
                ))}
              </Group>
              <ThemeToggle variant="icon" size="md" />
            </Group>

            {/* Mobile Menu Button */}
            <Burger
              opened={opened}
              onClick={toggle}
              hiddenFrom="sm"
              size="sm"
              color="cyan"
            />
          </Group>
        </Container>
      </header>

      {/* Mobile Navigation Drawer */}
      <Drawer
        opened={opened}
        onClose={close}
        title={
          <Group gap="xs">
            <IconRocket size={24} className="text-cyan-400" />
            <Text fw={700} className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
              硅基茶馆2077
            </Text>
          </Group>
        }
        padding="md"
        size="xs"
        position="right"
        styles={{
          header: {
            backgroundColor: 'rgba(15, 23, 42, 0.95)',
            borderBottom: '1px solid rgba(71, 85, 105, 0.3)',
          },
          body: {
            backgroundColor: 'rgba(15, 23, 42, 0.95)',
            padding: 0,
          },
          content: {
            backgroundColor: 'rgba(15, 23, 42, 0.95)',
          },
        }}
      >
        <Stack gap="md" p="md">
          <Stack gap="xs">
            {navItems.map((item) => (
              <NavLink key={item.href} {...item} mobile />
            ))}
          </Stack>

          <div className="border-t border-slate-700/30 pt-4">
            <Text size="sm" c="dimmed" mb="sm">主题设置</Text>
            <ThemeToggle variant="segmented" size="sm" />
          </div>
        </Stack>
      </Drawer>
    </>
  );
}