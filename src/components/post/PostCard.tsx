import Link from 'next/link';
import { PostData } from '@/lib/posts';

interface PostCardProps {
  post: PostData;
}

export default function PostCard({ post }: PostCardProps) {
  // post.slug is the date folder (e.g., 2025-05-25)
  // post.articleSlug is the markdown file name without .md (e.g., cursor-request-optimization)
  const editUrl = `/posts/${post.slug}`; // Link to the markdown editor page
  // const wechatPreviewUrl = `/posts/${post.slug}/${post.articleSlug}`; // Future link to WeChat TSX preview

  // 确保日期是字符串格式
  const dateStr = typeof post.date === 'object' ? 
    (post.date as unknown as Date).toISOString().split('T')[0] : 
    String(post.date);

  return (
    <Link href={editUrl}> 
      <div className="group bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/70 hover:border-slate-600/50 transition-all duration-200 cursor-pointer h-full">
        <div className="flex flex-col h-full">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white group-hover:text-cyan-400 transition-colors line-clamp-2 mb-1">
                {post.title}
              </h3>
              <div className="flex items-center gap-2 text-sm text-slate-400 mb-2">
                <span>📅</span>
                <time>{dateStr}</time> {/* 使用转换后的字符串日期 */}
                <span>•</span>
                {/* Since we only have .md now, this badge is less critical, but kept for consistency */}
                <span className={'px-2 py-1 rounded text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30'}>
                  📝 Markdown
                </span>
              </div>
            </div>
            <div className="text-2xl opacity-60 group-hover:opacity-100 transition-opacity">
              📝
            </div>
          </div>
          
          <p className="text-slate-300 text-sm leading-relaxed flex-1 line-clamp-3">
            {post.excerpt}
          </p>
          
          <div className="flex items-center justify-between mt-4 pt-3 border-t border-slate-700/50">
            <div className="flex items-center gap-2 text-xs text-slate-500">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>源文件</span>
            </div>
            <div className="text-cyan-400 group-hover:text-cyan-300 transition-colors text-sm font-medium">
              编辑 →
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
} 