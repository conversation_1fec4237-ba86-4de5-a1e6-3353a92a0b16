'use client';

import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { useTheme } from '@/contexts/ThemeContext';
import { getThemeConfig } from '@/lib/theme';

interface MantineThemeProviderProps {
  children: React.ReactNode;
}

export function MantineThemeProvider({ children }: MantineThemeProviderProps) {
  const { themeMode, colorScheme } = useTheme();
  const theme = getThemeConfig(themeMode);

  return (
    <MantineProvider theme={theme} defaultColorScheme={colorScheme}>
      <Notifications />
      {children}
    </MantineProvider>
  );
}
