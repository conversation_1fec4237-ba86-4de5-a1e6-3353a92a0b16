'use client';

import { 
  <PERSON><PERSON>, 
  Card, 
  Text, 
  Group, 
  Stack,
  Container,
  Title,
  Paper,
  Badge,
  Divider as <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON>,
  Code
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { 
  IconCheck, 
  IconInfoCircle,
  IconCode,
  IconPalette,
  IconDeviceMobile
} from '@tabler/icons-react';

// 导入现有的微信文章组件
import { 
  ImageContainer, 
  HighlightBox, 
  CodeBlock, 
  Section, 
  Divider 
} from '../posts/components';

export default function MantineIntegrationPage() {
  const showCompatibilityNotification = () => {
    notifications.show({
      title: '🎉 兼容性验证成功',
      message: 'Mantine UI 与现有微信文章组件完美兼容！',
      color: 'cyan',
      icon: <IconCheck size={16} />,
      autoClose: 4000,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Container size="lg" py="xl">
        <Stack gap="xl">
          {/* 标题区域 */}
          <Paper 
            p="xl" 
            radius="lg" 
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50"
          >
            <Group justify="space-between" align="center">
              <div>
                <Title 
                  order={1} 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400"
                >
                  🔗 Mantine × 微信文章组件兼容性展示
                </Title>
                <Text size="lg" c="dimmed" mt="xs">
                  验证 Mantine UI 与现有微信公众号组件库的完美融合
                </Text>
              </div>
              <Badge 
                variant="gradient" 
                gradient={{ from: 'cyan', to: 'pink' }}
                size="lg"
              >
                兼容性测试
              </Badge>
            </Group>
          </Paper>

          {/* Mantine 组件展示区域 */}
          <Section title="🎨 Mantine UI 组件展示">
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Text fw={500} size="lg" mb="md" className="text-cyan-400">
                现代化 UI 组件
              </Text>
              
              <Stack gap="md">
                <Group>
                  <Button 
                    variant="gradient" 
                    gradient={{ from: 'cyan', to: 'pink' }}
                    leftSection={<IconPalette size={16} />}
                  >
                    赛博朋克按钮
                  </Button>
                  <Button 
                    variant="outline" 
                    color="cyan"
                    leftSection={<IconCode size={16} />}
                  >
                    代码风格
                  </Button>
                  <Button 
                    variant="light" 
                    color="pink"
                    leftSection={<IconDeviceMobile size={16} />}
                  >
                    移动优化
                  </Button>
                </Group>

                <Alert 
                  icon={<IconInfoCircle size={16} />} 
                  title="集成说明" 
                  color="cyan"
                  variant="light"
                >
                  Mantine UI 组件采用赛博朋克主题配色，与硅基茶馆2077品牌风格保持一致。
                  所有组件都经过移动端优化，确保在微信环境中的良好显示效果。
                </Alert>

                <Paper p="md" withBorder>
                  <Text size="sm" c="dimmed" mb="xs">代码高亮示例</Text>
                  <Code block>
                    {`import { Button, Card } from '@mantine/core';

// 赛博朋克风格按钮
<Button variant="gradient" gradient={{ from: 'cyan', to: 'pink' }}>
  硅基茶馆2077
</Button>`}
                  </Code>
                </Paper>
              </Stack>
            </Card>
          </Section>

          <Divider />

          {/* 现有微信组件展示区域 */}
          <Section title="📱 现有微信文章组件展示">
            <HighlightBox type="info" title="组件兼容性验证">
              <p>以下展示了现有的微信公众号文章组件，验证它们与 Mantine UI 的完美兼容性：</p>
            </HighlightBox>

            <ImageContainer 
              src="/assets/series/cursor/cursor.png" 
              alt="硅基茶馆2077 Logo" 
              caption="硅基茶馆2077 - 探索未来科技的边界"
            />

            <CodeBlock 
              language="typescript" 
              title="TypeScript 示例代码"
            >
{`// Mantine 与现有组件的完美融合
import { MantineProvider } from '@mantine/core';
import { ImageContainer, HighlightBox } from '../posts/components';

export default function CyberpunkArticle() {
  return (
    <MantineProvider theme={cyberpunkTheme}>
      <ImageContainer src="/cyber-image.jpg" alt="赛博朋克" />
      <HighlightBox type="success">
        未来科技，触手可及！
      </HighlightBox>
    </MantineProvider>
  );
}`}
            </CodeBlock>

            <HighlightBox type="success" title="兼容性确认">
              <p>✅ 现有微信文章组件完全兼容</p>
              <p>✅ 样式不冲突，各自独立工作</p>
              <p>✅ 移动端显示效果良好</p>
              <p>✅ 赛博朋克主题风格统一</p>
            </HighlightBox>
          </Section>

          <Divider />

          {/* 混合使用展示 */}
          <Section title="🔄 混合使用展示">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Mantine 卡片 */}
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Text fw={500} size="lg" mb="md" className="text-cyan-400">
                  Mantine 风格卡片
                </Text>
                <Text size="sm" c="dimmed" mb="md">
                  使用 Mantine 的现代化组件系统，提供丰富的交互体验。
                </Text>
                <Button 
                  variant="gradient" 
                  gradient={{ from: 'cyan', to: 'pink' }}
                  fullWidth
                  onClick={showCompatibilityNotification}
                >
                  测试兼容性
                </Button>
              </Card>

              {/* 传统微信组件风格 */}
              <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-pink-400 mb-4">
                  微信文章组件风格
                </h3>
                <p className="text-slate-300 text-sm mb-4">
                  保持原有的微信公众号文章组件风格，确保内容的一致性和可读性。
                </p>
                <HighlightBox type="warning">
                  两种风格可以在同一页面中和谐共存！
                </HighlightBox>
              </div>
            </div>
          </Section>

          <MantineDivider 
            my="xl" 
            label="集成完成" 
            labelPosition="center"
            color="cyan"
          />

          {/* 总结区域 */}
          <Paper 
            p="lg" 
            radius="md" 
            className="bg-gradient-to-r from-cyan-500/10 to-pink-500/10 border border-cyan-500/20"
          >
            <Group justify="space-between" align="center">
              <div>
                <Text fw={500} size="lg" className="text-cyan-400">
                  🎉 集成成功总结
                </Text>
                <Text size="sm" c="dimmed" mt="xs">
                  Mantine UI 已成功集成到硅基茶馆2077项目中，与现有组件完美兼容
                </Text>
              </div>
              <Button 
                variant="gradient" 
                gradient={{ from: 'cyan', to: 'pink' }}
                onClick={() => window.location.href = '/mantine-test'}
              >
                查看更多示例
              </Button>
            </Group>
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
