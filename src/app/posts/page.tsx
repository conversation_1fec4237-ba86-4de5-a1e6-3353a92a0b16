'use client';

import { useState, useMemo } from 'react';
import Link from 'next/link';
import { 
  Container, 
  Title, 
  Text, 
  Grid, 
  Card, 
  Group, 
  Badge, 
  Stack,
  TextInput,
  Select,
  Pagination,
  ActionIcon,
  Tooltip,
  Paper
} from '@mantine/core';
import { 
  IconSearch, 
  IconCalendar, 
  IconEye, 
  IconExternalLink,
  IconFilter,
  IconSortDescending,
  IconArticle
} from '@tabler/icons-react';
import { Header } from '@/components/layout';
import { useTheme } from '@/contexts/ThemeContext';

// 模拟文章数据
const mockPosts = [
  {
    id: '2025-05-25',
    title: 'Cursor AI 编程助手深度体验',
    excerpt: '探索 Cursor AI 如何革命性地改变编程体验，从代码补全到智能重构的全方位分析。',
    date: '2025-05-25',
    category: 'AI工具',
    tags: ['Cursor', 'AI', '编程助手', '开发工具'],
    readTime: '8 分钟',
    views: 1234,
    status: 'published',
    featured: true,
  },
  {
    id: '2025-05-30',
    title: '未来科技趋势预测',
    excerpt: '分析2025年最值得关注的科技趋势，从人工智能到量子计算的前沿探索。',
    date: '2025-05-30',
    category: '科技趋势',
    tags: ['未来科技', '趋势分析', 'AI', '量子计算'],
    readTime: '12 分钟',
    views: 856,
    status: 'published',
    featured: false,
  },
  {
    id: '2025-06-01',
    title: '赛博朋克美学在现代设计中的应用',
    excerpt: '探讨赛博朋克风格如何影响现代UI/UX设计，以及如何在项目中实现这种美学。',
    date: '2025-06-01',
    category: '设计',
    tags: ['赛博朋克', 'UI设计', '美学', '视觉设计'],
    readTime: '6 分钟',
    views: 642,
    status: 'published',
    featured: true,
  },
];

const categories = ['全部', 'AI工具', '科技趋势', '设计', '开发'];
const sortOptions = [
  { value: 'date-desc', label: '最新发布' },
  { value: 'date-asc', label: '最早发布' },
  { value: 'views-desc', label: '最多浏览' },
  { value: 'title-asc', label: '标题 A-Z' },
];

export default function PostsPage() {
  const { colorScheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [sortBy, setSortBy] = useState('date-desc');
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 6;

  // 过滤和排序文章
  const filteredAndSortedPosts = useMemo(() => {
    let filtered = mockPosts.filter(post => {
      const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === '全部' || post.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        case 'date-asc':
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        case 'views-desc':
          return b.views - a.views;
        case 'title-asc':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchQuery, selectedCategory, sortBy]);

  // 分页
  const totalPages = Math.ceil(filteredAndSortedPosts.length / postsPerPage);
  const paginatedPosts = filteredAndSortedPosts.slice(
    (currentPage - 1) * postsPerPage,
    currentPage * postsPerPage
  );

  const PostCard = ({ post }: { post: typeof mockPosts[0] }) => (
    <Card
      shadow="sm"
      padding="lg"
      radius="lg"
      withBorder
      className={`h-full transition-all duration-300 cursor-pointer group hover:transform hover:-translate-y-1 ${
        colorScheme === 'dark' 
          ? 'bg-slate-800/50 backdrop-blur-sm border-slate-700/50 hover:border-cyan-500/30' 
          : 'bg-white/90 backdrop-blur-sm border-cyan-200/50 hover:border-cyan-400/50'
      }`}
      component={Link}
      href={`/posts/${post.id}`}
      style={{ textDecoration: 'none' }}
    >
      <Stack gap="md" h="100%">
        <Group justify="space-between" align="flex-start">
          <Badge 
            variant="light" 
            color="cyan" 
            size="sm"
            className="group-hover:bg-cyan-500/20"
          >
            {post.category}
          </Badge>
          {post.featured && (
            <Badge variant="gradient" gradient={{ from: 'cyan', to: 'pink' }} size="sm">
              精选
            </Badge>
          )}
        </Group>

        <Stack gap="xs" style={{ flex: 1 }}>
          <Title 
            order={3} 
            size="1.2rem" 
            className={`group-hover:text-cyan-400 transition-colors line-clamp-2 ${
              colorScheme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}
          >
            {post.title}
          </Title>
          
          <Text 
            c="dimmed" 
            size="sm" 
            className="line-clamp-3"
            style={{ lineHeight: 1.6 }}
          >
            {post.excerpt}
          </Text>
        </Stack>

        <Stack gap="xs">
          <Group gap="xs" wrap="wrap">
            {post.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" size="xs" color="gray">
                {tag}
              </Badge>
            ))}
            {post.tags.length > 3 && (
              <Badge variant="outline" size="xs" color="gray">
                +{post.tags.length - 3}
              </Badge>
            )}
          </Group>

          <Group justify="space-between" align="center">
            <Group gap="xs">
              <IconCalendar size={14} className="text-slate-400" />
              <Text size="xs" c="dimmed">{post.date}</Text>
              <Text size="xs" c="dimmed">·</Text>
              <Text size="xs" c="dimmed">{post.readTime}</Text>
            </Group>
            
            <Group gap="xs">
              <IconEye size={14} className="text-slate-400" />
              <Text size="xs" c="dimmed">{post.views}</Text>
              <Tooltip label="查看文章">
                <ActionIcon 
                  variant="subtle" 
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <IconExternalLink size={14} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>
        </Stack>
      </Stack>
    </Card>
  );

  return (
    <div className={`min-h-screen ${
      colorScheme === 'dark' 
        ? 'bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900' 
        : 'bg-gradient-to-br from-blue-50 via-cyan-50 to-pink-50'
    }`}>
      <Header />
      
      <Container size="xl" py="xl">
        {/* 页面标题 */}
        <Stack gap="xl" align="center" mb="xl">
          <Group gap="xs">
            <IconArticle size={32} className="text-cyan-400" />
            <Title 
              order={1} 
              size="3rem"
              className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 text-center"
            >
              文章列表
            </Title>
          </Group>
          <Text size="lg" c="dimmed" ta="center" maw={600}>
            探索硅基茶馆2077的技术洞察与未来思考
          </Text>
        </Stack>

        {/* 搜索和筛选 */}
        <Paper 
          p="lg" 
          radius="lg" 
          mb="xl"
          className={`${
            colorScheme === 'dark' 
              ? 'bg-slate-800/50 backdrop-blur-sm border border-slate-700/50' 
              : 'bg-white/80 backdrop-blur-sm border border-cyan-200/50'
          }`}
        >
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <TextInput
                placeholder="搜索文章标题、内容或标签..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                size="md"
              />
            </Grid.Col>
            
            <Grid.Col span={{ base: 6, md: 3 }}>
              <Select
                placeholder="选择分类"
                leftSection={<IconFilter size={16} />}
                data={categories}
                value={selectedCategory}
                onChange={(value) => setSelectedCategory(value || '全部')}
                size="md"
              />
            </Grid.Col>
            
            <Grid.Col span={{ base: 6, md: 3 }}>
              <Select
                placeholder="排序方式"
                leftSection={<IconSortDescending size={16} />}
                data={sortOptions}
                value={sortBy}
                onChange={(value) => setSortBy(value || 'date-desc')}
                size="md"
              />
            </Grid.Col>
          </Grid>
        </Paper>

        {/* 文章网格 */}
        {paginatedPosts.length > 0 ? (
          <>
            <Grid>
              {paginatedPosts.map((post) => (
                <Grid.Col key={post.id} span={{ base: 12, md: 6, lg: 4 }}>
                  <PostCard post={post} />
                </Grid.Col>
              ))}
            </Grid>

            {/* 分页 */}
            {totalPages > 1 && (
              <Group justify="center" mt="xl">
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="md"
                  color="cyan"
                />
              </Group>
            )}
          </>
        ) : (
          <Paper 
            p="xl" 
            radius="lg" 
            ta="center"
            className={`${
              colorScheme === 'dark' 
                ? 'bg-slate-800/50 backdrop-blur-sm border border-slate-700/50' 
                : 'bg-white/80 backdrop-blur-sm border border-cyan-200/50'
            }`}
          >
            <Stack gap="md" align="center">
              <IconArticle size={48} className="text-slate-400" />
              <Title order={3} c="dimmed">暂无文章</Title>
              <Text c="dimmed">没有找到符合条件的文章，请尝试调整搜索条件。</Text>
            </Stack>
          </Paper>
        )}
      </Container>
    </div>
  );
}
