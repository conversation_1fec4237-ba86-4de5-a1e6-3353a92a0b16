'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/layout';

export default function NewPostPage() {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      alert('请输入文章标题');
      return;
    }

    setIsLoading(true);
    
    try {
      // 使用当前日期作为 slug
      const today = new Date();
      const slug = today.toISOString().split('T')[0]; // YYYY-MM-DD 格式
      
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          slug,
          title: title.trim(),
          content: content.trim(),
        }),
      });

      if (response.ok) {
        router.push(`/posts/${slug}`);
      } else {
        throw new Error('创建文章失败');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      alert('创建文章失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-2">
              ✨ 创建新文章
            </h1>
            <p className="text-slate-300">
              开始您的创作之旅
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-slate-300 mb-2">
                文章标题 *
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="输入您的文章标题..."
                className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label htmlFor="content" className="block text-sm font-medium text-slate-300 mb-2">
                文章内容
              </label>
              <textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="开始编写您的文章内容...

支持 Markdown 语法：
# 标题
**粗体** *斜体*
- 列表项
[链接](url)
![图片](image-url)
"
                rows={20}
                className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent font-mono"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
              <div className="text-sm text-slate-400">
                文章将自动保存到：<code className="bg-slate-700 px-2 py-1 rounded">{new Date().toISOString().split('T')[0]}/</code>
              </div>
              
              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-6 py-3 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-800/50 transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? '创建中...' : '创建文章'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
} 