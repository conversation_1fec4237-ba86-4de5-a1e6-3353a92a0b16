import Link from 'next/link';
// import { Header } from '@/components/layout';
// import { Container, Grid, Card, Text, Title, Badge, Group, Stack, List, ThemeIcon } from '@mantine/core';
// import { IconCheck, IconArrowRight } from '@tabler/icons-react';

export default function ToolsPage() {
  const tools = [
    {
      id: 'article-converter',
      title: '文章页面转换器',
      description: '将基础 HTML 代码转换为使用项目组件库的高质量文章页面，适配微信公众号样式规范。',
      icon: '📄',
      href: '/tools/article-converter',
      features: [
        '智能组件映射 (img → ImageContainer)',
        '自动章节结构化 (Section + Divider)',
        '代码块和提示框转换',
        '表格样式优化',
        '完整页面代码生成'
      ]
    },
    {
      id: 'html-to-tsx',
      title: 'HTML 到 TSX 转换器',
      description: '将原生 HTML 代码转换为 React TSX 语法，支持属性名转换、自闭合标签、内联样式对象化等功能。',
      icon: '🔄',
      href: '/tools/html-to-tsx',
      features: [
        '属性名自动转换 (class → className)',
        '自闭合标签格式化',
        '内联样式对象化',
        '事件处理器转换',
        '实时预览和验证'
      ]
    },
    {
      id: 'cover-generator',
      title: '封面生成器',
      description: '为微信公众号文章生成专业的封面图片，支持多种预设模板和自定义配置。',
      icon: '🎨',
      href: '/cover',
      features: [
        '多种预设模板',
        '自定义标题和副标题',
        '高质量图片生成',
        '一键复制到剪贴板',
        '移动端适配'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4">
            开发工具集
          </h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            提升开发效率的实用工具集合，专为硅基茶馆2077项目优化
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {tools.map((tool) => (
            <Link key={tool.id} href={tool.href} className="block">
              <div className="h-full bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:bg-slate-800/70 hover:border-slate-600/50 transition-all duration-300 cursor-pointer group hover:transform hover:-translate-y-1 rounded-lg p-6">
                <div className="flex flex-col h-full">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="text-5xl">{tool.icon}</div>
                    <div className="flex-1">
                      <h2 className="text-xl font-bold text-white group-hover:text-cyan-400 transition-colors mb-2">
                        {tool.title}
                      </h2>
                      <p className="text-slate-400 text-sm leading-relaxed">
                        {tool.description}
                      </p>
                    </div>
                  </div>

                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-cyan-400 mb-2">主要功能</h3>
                    <ul className="space-y-1">
                      {tool.features.map((feature, index) => (
                        <li key={index} className="text-slate-400 text-sm flex items-center">
                          <span className="w-1.5 h-1.5 bg-cyan-400 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex justify-between items-center pt-4 mt-4 border-t border-slate-700/30">
                    <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
                      可用
                    </span>
                    <div className="flex items-center gap-2 text-cyan-400 group-hover:text-cyan-300 transition-colors">
                      <span className="text-sm font-medium">使用工具</span>
                      <span>→</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            更多工具正在开发中...
          </div>
        </div>
      </div>
    </div>
  );
}
