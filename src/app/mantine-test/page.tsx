'use client';

import { 
  But<PERSON>, 
  Card, 
  Text, 
  Group, 
  Stack,
  TextInput,
  Select,
  Notification,
  Badge,
  Paper,
  Title,
  Container,
  Grid,
  ActionIcon,
  Tooltip
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconCheck,
  IconRocket,
  IconCode, 
  IconPalette,
  IconBrandReact,
  IconSparkles
} from '@tabler/icons-react';
import { useState } from 'react';

export default function MantineTestPage() {
  const [showSuccess, setShowSuccess] = useState(false);
  
  const form = useForm({
    initialValues: {
      name: '',
      email: '',
      framework: '',
      theme: 'cyberpunk',
    },
    validate: {
      name: (value) => value.length < 2 ? '名称至少需要2个字符' : null,
      email: (value) => (/^\S+@\S+$/.test(value) ? null : '请输入有效的邮箱地址'),
    },
  });

  const handleSubmit = (values: typeof form.values) => {
    notifications.show({
      title: '🚀 集成测试成功',
      message: `欢迎来到硅基茶馆2077，${values.name}！`,
      color: 'cyan',
      icon: <IconRocket size={16} />,
      autoClose: 5000,
    });
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Container size="lg" py="xl">
        <Stack gap="xl">
          {/* 标题区域 */}
          <Paper 
            p="xl" 
            radius="lg" 
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50"
          >
            <Group justify="space-between" align="center">
              <div>
                <Title 
                  order={1} 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400"
                >
                  🤖 Mantine UI 集成测试
                </Title>
                <Text size="lg" c="dimmed" mt="xs">
                  硅基茶馆2077 × Mantine UI 赛博朋克风格验证
                </Text>
              </div>
              <Group>
                <Badge 
                  variant="gradient" 
                  gradient={{ from: 'cyan', to: 'pink' }}
                  size="lg"
                >
                  v8.0.2
                </Badge>
                <ActionIcon 
                  variant="gradient" 
                  gradient={{ from: 'cyan', to: 'pink' }}
                  size="lg"
                >
                  <IconSparkles size={20} />
                </ActionIcon>
              </Group>
            </Group>
          </Paper>

          {/* 功能展示网格 */}
          <Grid>
            {/* 按钮展示 */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Text fw={500} size="lg" mb="md" className="text-cyan-400">
                  🎨 按钮样式展示
                </Text>
                
                <Stack gap="md">
                  <Group>
                    <Button variant="filled" color="cyan">
                      主要按钮
                    </Button>
                    <Button variant="outline" color="pink">
                      次要按钮
                    </Button>
                  </Group>
                  
                  <Group>
                    <Button variant="light" color="cyan">
                      轻量按钮
                    </Button>
                    <Button 
                      variant="gradient" 
                      gradient={{ from: 'cyan', to: 'pink' }}
                    >
                      渐变按钮
                    </Button>
                  </Group>
                  
                  <Group>
                    <Tooltip label="代码相关功能">
                      <ActionIcon variant="filled" color="cyan" size="lg">
                        <IconCode size={20} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="React 组件">
                      <ActionIcon variant="outline" color="pink" size="lg">
                        <IconBrandReact size={20} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="主题配置">
                      <ActionIcon variant="light" color="violet" size="lg">
                        <IconPalette size={20} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>

            {/* 表单展示 */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Text fw={500} size="lg" mb="md" className="text-pink-400">
                  📝 表单组件展示
                </Text>
                
                <form onSubmit={form.onSubmit(handleSubmit)}>
                  <Stack gap="md">
                    <TextInput
                      label="用户名"
                      placeholder="请输入您的用户名"
                      {...form.getInputProps('name')}
                    />
                    
                    <TextInput
                      label="邮箱地址"
                      placeholder="请输入您的邮箱"
                      {...form.getInputProps('email')}
                    />
                    
                    <Select
                      label="技术栈偏好"
                      placeholder="选择您喜欢的技术栈"
                      data={[
                        { value: 'react', label: '⚛️ React' },
                        { value: 'vue', label: '💚 Vue' },
                        { value: 'angular', label: '🅰️ Angular' },
                        { value: 'svelte', label: '🧡 Svelte' },
                      ]}
                      {...form.getInputProps('framework')}
                    />
                    
                    <Button 
                      type="submit" 
                      variant="gradient"
                      gradient={{ from: 'cyan', to: 'pink' }}
                      fullWidth
                    >
                      🚀 提交测试
                    </Button>
                  </Stack>
                </form>
              </Card>
            </Grid.Col>
          </Grid>

          {/* 成功状态展示 */}
          {showSuccess && (
            <Notification
              icon={<IconCheck size={20} />}
              color="teal"
              title="🎉 集成验证成功！"
              onClose={() => setShowSuccess(false)}
              className="animate-pulse"
            >
              Mantine UI 已成功集成到硅基茶馆2077项目中，赛博朋克风格主题运行正常！
            </Notification>
          )}

          {/* 技术信息展示 */}
          <Paper 
            p="lg" 
            radius="md" 
            className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/30"
          >
            <Text fw={500} size="lg" mb="md" className="text-violet-400">
              🔧 技术栈信息
            </Text>
            <Grid>
              <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                <Text size="sm" c="dimmed">React</Text>
                <Text fw={500}>19.0.0</Text>
              </Grid.Col>
              <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                <Text size="sm" c="dimmed">Next.js</Text>
                <Text fw={500}>15.3.2</Text>
              </Grid.Col>
              <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                <Text size="sm" c="dimmed">Mantine</Text>
                <Text fw={500}>8.0.2</Text>
              </Grid.Col>
              <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                <Text size="sm" c="dimmed">主题</Text>
                <Text fw={500} className="text-cyan-400">赛博朋克</Text>
              </Grid.Col>
            </Grid>
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
