import type { Metada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from '@/contexts/ThemeContext';
import { MantineThemeProvider } from '@/components/providers/MantineThemeProvider';

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased`}>
        <ThemeProvider>
          <MantineThemeProvider>
            {children}
          </MantineThemeProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
