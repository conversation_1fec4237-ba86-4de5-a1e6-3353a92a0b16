@import "tailwindcss";

/* Mantine 样式导入 */
@import '@mantine/core/styles.css';
@import '@mantine/notifications/styles.css';

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* 赛博朋克主题色彩变量 */
  --cyber-cyan: #06b6d4;
  --cyber-pink: #ec4899;
  --cyber-purple: #8b5cf6;
  --cyber-dark: #0f172a;
  --cyber-darker: #020617;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* 只对没有 Tailwind 类的元素应用默认颜色 */
body:not([class*="text-"]):not([class*="bg-"]) {
  background: var(--background);
  color: var(--foreground);
}

/* 确保 Tailwind 类的优先级 */
[class*="text-"] {
  color: unset !important;
}

[class*="bg-"] {
  background: unset !important;
}

/* Mantine 与 Tailwind 兼容性样式 */
/* 确保 Mantine 组件样式优先级 */
.mantine-* {
  /* 保护 Mantine 组件不被 Tailwind 重置 */
}

/* 赛博朋克风格的 Mantine 组件覆盖 */
.mantine-Button-root {
  transition: all 0.3s ease;
}

.mantine-Button-root:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(6, 182, 212, 0.3);
}

/* 深色主题下的 Mantine 组件优化 */
@media (prefers-color-scheme: dark) {
  .mantine-Paper-root {
    background-color: rgba(15, 23, 42, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.3) !important;
  }

  .mantine-Card-root {
    background-color: rgba(30, 41, 59, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.3) !important;
  }
}
