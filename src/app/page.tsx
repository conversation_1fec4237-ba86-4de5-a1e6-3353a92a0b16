'use client';

import Link from 'next/link';
import { Header } from '@/components/layout';
import {
  Container,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Card,
  Badge,
  ActionIcon,
  Paper,

} from '@mantine/core';
import {
  IconRocket,
  IconArticle,
  IconPalette,
  IconTools,
  IconCode,

  IconExternalLink,
  IconSparkles,
  IconTrendingUp,
  IconUsers,
  IconBolt
} from '@tabler/icons-react';
import { CyberButton } from '@/components/ui';
import { useTheme } from '@/contexts/ThemeContext';
import { getAllPosts } from '@/lib/posts';

// 功能特性数据
const features = [
  {
    icon: IconArticle,
    title: '智能文章编辑',
    description: '支持 Markdown 语法，实时预览，专为技术写作优化',
    color: 'cyan',
    href: '/posts/new',
  },
  {
    icon: IconPalette,
    title: '封面生成器',
    description: '一键生成专业的文章封面，多种模板可选',
    color: 'pink',
    href: '/cover',
  },
  {
    icon: IconTools,
    title: '开发工具集',
    description: 'HTML 转换、代码格式化等实用开发工具',
    color: 'purple',
    href: '/tools',
  },
  {
    icon: IconCode,
    title: '代码高亮',
    description: '支持多种编程语言的语法高亮显示',
    color: 'blue',
    href: '/tools/html-to-tsx',
  },
];

// 统计数据 - 动态计算
const getStats = (postsCount: number) => [
  { icon: IconTrendingUp, label: '文章总数', value: `${postsCount}`, color: 'cyan' },
  { icon: IconUsers, label: '访问量', value: '1.2K+', color: 'pink' },
  { icon: IconBolt, label: '工具数量', value: '3', color: 'purple' },
  { icon: IconSparkles, label: '功能特性', value: '12+', color: 'blue' },
];

export default function Home() {
  // 获取真实文章数据
  const posts = getAllPosts();
  const { colorScheme } = useTheme();
  const stats = getStats(posts.length);

  return (
    <div className={`min-h-screen ${
      colorScheme === 'dark'
        ? 'bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900'
        : 'bg-gradient-to-br from-blue-50 via-cyan-50 to-pink-50'
    }`}>
      <Header />

      <Container size="xl" py="xl">
        {/* Hero 区域 */}
        <Stack gap="xl" align="center" mb="xl">
          <Group gap="md" align="center">
            <IconRocket
              size={48}
              className="text-cyan-400 animate-pulse"
            />
            <Title
              order={1}
              size="4rem"
              className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 text-center"
              style={{ lineHeight: 1.1 }}
            >
              硅基茶馆2077
            </Title>
          </Group>

          <Text
            size="xl"
            c="dimmed"
            ta="center"
            maw={700}
            style={{ lineHeight: 1.6 }}
          >
            探索未来科技与思考的交汇点，在赛博朋克的世界中寻找技术的诗意
          </Text>

          <Group gap="md" justify="center">
            <Link href="/posts/new" style={{ textDecoration: 'none' }}>
              <CyberButton
                variant="primary"
                size="lg"
                leftSection={<IconSparkles size={20} />}
              >
                开始创作
              </CyberButton>
            </Link>
            <Link href="/cover" style={{ textDecoration: 'none' }}>
              <CyberButton
                variant="outline"
                size="lg"
                leftSection={<IconPalette size={20} />}
              >
                制作封面
              </CyberButton>
            </Link>
          </Group>
        </Stack>

        {/* 统计数据 */}
        <Paper
          p="xl"
          radius="lg"
          mb="xl"
          className={`${
            colorScheme === 'dark'
              ? 'bg-slate-800/30 backdrop-blur-sm border border-slate-700/30'
              : 'bg-white/60 backdrop-blur-sm border border-cyan-200/30'
          }`}
        >
          <Grid>
            {stats.map((stat, index) => (
              <Grid.Col key={index} span={{ base: 6, md: 3 }}>
                <Group gap="md" align="center">
                  <ActionIcon
                    size="xl"
                    variant="light"
                    color={stat.color}
                    className="animate-pulse"
                  >
                    <stat.icon size={24} />
                  </ActionIcon>
                  <Stack gap={0}>
                    <Text size="xl" fw={700} className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
                      {stat.value}
                    </Text>
                    <Text size="sm" c="dimmed">
                      {stat.label}
                    </Text>
                  </Stack>
                </Group>
              </Grid.Col>
            ))}
          </Grid>
        </Paper>

        {/* 功能特性 */}
        <Stack gap="xl" mb="xl">
          <Group justify="center">
            <Title order={2} size="2.5rem" ta="center" className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
              核心功能
            </Title>
          </Group>

          <Grid>
            {features.map((feature, index) => (
              <Grid.Col key={index} span={{ base: 12, md: 6, lg: 3 }}>
                <Card
                  shadow="sm"
                  padding="xl"
                  radius="lg"
                  withBorder
                  className={`h-full transition-all duration-300 cursor-pointer group hover:transform hover:-translate-y-2 ${
                    colorScheme === 'dark'
                      ? 'bg-slate-800/50 backdrop-blur-sm border-slate-700/50 hover:border-cyan-500/30'
                      : 'bg-white/80 backdrop-blur-sm border-cyan-200/50 hover:border-cyan-400/50'
                  }`}
                  component={Link}
                  href={feature.href}
                  style={{ textDecoration: 'none' }}
                >
                  <Stack gap="md" align="center" ta="center">
                    <ActionIcon
                      size="xl"
                      variant="gradient"
                      gradient={{ from: 'cyan', to: 'pink' }}
                      className="group-hover:scale-110 transition-transform duration-300"
                    >
                      <feature.icon size={28} />
                    </ActionIcon>

                    <Stack gap="xs">
                      <Title
                        order={3}
                        size="1.2rem"
                        className={`group-hover:text-cyan-400 transition-colors ${
                          colorScheme === 'dark' ? 'text-white' : 'text-slate-900'
                        }`}
                      >
                        {feature.title}
                      </Title>
                      <Text c="dimmed" size="sm" style={{ lineHeight: 1.6 }}>
                        {feature.description}
                      </Text>
                    </Stack>

                    <Badge
                      variant="light"
                      color={feature.color}
                      className="group-hover:bg-cyan-500/20 transition-colors"
                    >
                      立即体验
                    </Badge>
                  </Stack>
                </Card>
              </Grid.Col>
            ))}
          </Grid>
        </Stack>

        {/* 最新文章 */}
        <Stack gap="xl">
          <Group justify="space-between" align="center">
            <Title order={2} size="2rem" className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
              最新文章
            </Title>
            <Link href="/posts" style={{ textDecoration: 'none' }}>
              <CyberButton
                variant="ghost"
                rightSection={<IconExternalLink size={16} />}
              >
                查看全部
              </CyberButton>
            </Link>
          </Group>

          {posts.length > 0 ? (
            <Grid>
              {posts.slice(0, 6).map((post) => (
                <Grid.Col key={post.slug} span={{ base: 12, md: 6, lg: 4 }}>
                  <Card p="lg" radius="lg" withBorder>
                    <Title order={3}>{post.title}</Title>
                    <Text c="dimmed" mt="xs">{post.excerpt}</Text>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>
          ) : (
            <Paper
              p="xl"
              radius="lg"
              ta="center"
              className={`${
                colorScheme === 'dark'
                  ? 'bg-slate-800/50 backdrop-blur-sm border border-slate-700/50'
                  : 'bg-white/80 backdrop-blur-sm border border-cyan-200/50'
              }`}
            >
              <Stack gap="md" align="center">
                <IconArticle size={64} className="text-slate-400" />
                <Title order={3} c="dimmed">还没有文章</Title>
                <Text c="dimmed" maw={400}>
                  开始您的创作之旅，分享您的技术洞察和未来思考
                </Text>
                <Link href="/posts/new" style={{ textDecoration: 'none' }}>
                  <CyberButton
                    variant="primary"
                    leftSection={<IconSparkles size={16} />}
                  >
                    创建第一篇文章
                  </CyberButton>
                </Link>
              </Stack>
            </Paper>
          )}
        </Stack>

        {/* 底部状态 */}
        <Group justify="center" mt="xl">
          <Badge
            size="lg"
            variant="gradient"
            gradient={{ from: 'cyan', to: 'pink' }}
            leftSection={
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            }
          >
            共 {posts.length} 篇文章 · 持续更新中
          </Badge>
        </Group>
      </Container>
    </div>
  );
}
