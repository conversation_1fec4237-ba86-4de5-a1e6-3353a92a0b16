'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { MantineColorScheme } from '@mantine/core';
import { ThemeMode, getSystemColorScheme } from '@/lib/theme';

interface ThemeContextType {
  themeMode: ThemeMode;
  colorScheme: MantineColorScheme;
  setThemeMode: (mode: ThemeMode) => void;
  toggleColorScheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeModeState] = useState<ThemeMode>('dark');
  const [colorScheme, setColorScheme] = useState<MantineColorScheme>('dark');

  // 从 localStorage 加载主题设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme-mode') as ThemeMode;
      if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
        setThemeModeState(savedTheme);
      }
    }
  }, []);

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window !== 'undefined' && themeMode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = () => {
        setColorScheme(getSystemColorScheme());
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [themeMode]);

  // 更新颜色方案
  useEffect(() => {
    let newColorScheme: MantineColorScheme;
    
    switch (themeMode) {
      case 'light':
        newColorScheme = 'light';
        break;
      case 'dark':
        newColorScheme = 'dark';
        break;
      case 'auto':
        newColorScheme = getSystemColorScheme();
        break;
      default:
        newColorScheme = 'dark';
    }
    
    setColorScheme(newColorScheme);
  }, [themeMode]);

  const setThemeMode = (mode: ThemeMode) => {
    setThemeModeState(mode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme-mode', mode);
    }
  };

  const toggleColorScheme = () => {
    const newMode = colorScheme === 'dark' ? 'light' : 'dark';
    setThemeMode(newMode);
  };

  const value: ThemeContextType = {
    themeMode,
    colorScheme,
    setThemeMode,
    toggleColorScheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
