/* src/styles/wechat-article.css */

/* Styles extracted from Cursor请求优化_微信版.html will be placed here */
/* Example base styles - replace with actual extracted styles */

/* 微信文章预览样式 */
.wechat-article-body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    padding: 0;
    margin: 0;
    word-wrap: break-word;
    word-break: break-all;
}

.wechat-article-container {
    max-width: 100%;
    padding: 20px 16px;
    margin: 0 auto;
}

.wechat-article-title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.4;
    padding: 20px 10px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.wechat-article-title::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 179, 0.1), transparent);
    animation: titleScan 4s infinite;
}

@keyframes titleScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.wechat-article-content {
    font-size: 16px;
    line-height: 1.8;
}

.wechat-article-content h2 {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 30px 0 15px 0;
    padding: 12px 16px 12px 16px;
    border-bottom: 2px solid #00d4ff;
    border-left: 4px solid #ff6bb3;
    background: linear-gradient(90deg, rgba(255, 107, 179, 0.08) 0%, transparent 50%);
    position: relative;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.1);
}

.wechat-article-content h2::before {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #ff6bb3, #00d4ff);
    animation: wechatH2Glow 2s ease-in-out infinite alternate;
}

@keyframes wechatH2Glow {
    0% { box-shadow: 0 0 5px #ff6bb3; }
    100% { box-shadow: 0 0 15px #00d4ff; }
}

.wechat-article-content h3 {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 25px 0 12px 0;
    padding: 8px 0 8px 16px;
    border-left: 4px solid #ff6bb3;
    background: linear-gradient(90deg, rgba(255, 107, 179, 0.05) 0%, transparent 30%);
    border-radius: 0 6px 6px 0;
    position: relative;
}

.wechat-article-content h3::after {
    content: "";
    position: absolute;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6bb3;
    font-size: 12px;
    animation: wechatPulse 2s infinite;
}

@keyframes wechatPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.wechat-article-content p {
    margin-bottom: 16px;
    text-align: justify;
    color: #333;
}

.wechat-article-content ul, .wechat-article-content ol {
    margin: 16px 0;
    padding-left: 20px;
}

.wechat-article-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.wechat-article-content strong {
    color: #ff6bb3;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(255, 107, 179, 0.3);
    position: relative;
}

.wechat-article-content a {
    color: #00d4ff;
    text-decoration: none;
    word-break: break-all;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

.wechat-article-content a:hover {
    color: #ff6bb3;
    text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
}

.wechat-article-content img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 20px auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.wechat-article-content pre {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid #00d4ff;
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0; /* Increased margin for pre blocks */
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);
}

.wechat-article-content pre::before {
    content: "● ● ●";
    position: absolute;
    top: 8px;
    left: 12px;
    color: #ff6bb3;
    font-size: 12px;
    letter-spacing: 4px;
}

.wechat-article-content pre::after {
    content: "Terminal";
    position: absolute;
    top: 8px;
    right: 12px;
    color: #00d4ff;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.wechat-article-content code {
    font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    background-color: rgba(255, 107, 179, 0.1); /* Light pink background for inline code */
    padding: 3px 6px; /* Adjusted padding */
    border-radius: 4px; /* Rounded corners for inline code */
    font-size: 0.9em; /* Slightly smaller font size for inline code */
    color: #ff6bb3;
    border: 1px solid rgba(255, 107, 179, 0.2);
}

.wechat-article-content pre code {
    background-color: transparent;
    padding: 20px 0 0 0;
    color: #00ff88; /* Greenish color for code block text */
    display: block;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
    font-size: 14px; /* Ensure pre code font size is consistent */
    border: none; /* Remove border from code inside pre */
}

.wechat-article-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00d4ff 20%, #ff6bb3 50%, #39ff14 80%, transparent 100%);
    margin: 40px 0;
    border: none;
    border-radius: 1px;
    position: relative;
    overflow: hidden;
}

.wechat-article-divider::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: wechatDividerScan 3s infinite;
}

@keyframes wechatDividerScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.wechat-highlight-box {
    background: linear-gradient(135deg, rgba(57, 255, 20, 0.08) 0%, rgba(57, 255, 20, 0.02) 100%);
    border-left: 4px solid #39ff14;
    border: 1px solid rgba(57, 255, 20, 0.3);
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 0 15px rgba(57, 255, 20, 0.1);
}

.wechat-highlight-box::before {
    content: "⚠";
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    background: #39ff14;
    color: #000;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    animation: wechatWarningPulse 2s infinite;
}

@keyframes wechatWarningPulse {
    0%, 100% { box-shadow: 0 0 5px #39ff14; }
    50% { box-shadow: 0 0 20px #39ff14; }
}

.wechat-highlight-box strong {
    color: #39ff14;
    text-shadow: 0 0 5px rgba(57, 255, 20, 0.3);
}

.wechat-section {
    margin-bottom: 30px;
    position: relative;
}

.wechat-section::before {
    content: "";
    position: absolute;
    top: -10px;
    left: 0;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, #ff6bb3, transparent);
    opacity: 0.6;
}

.wechat-intro-text {
    font-size: 17px;
    color: #666;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);
}

.wechat-intro-text::before {
    content: "";
    position: absolute;
    left: -12px;
    top: 20px;
    background: linear-gradient(135deg, #00d4ff, #ff6bb3);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    animation: wechatIntroPulse 3s infinite;
}

@keyframes wechatIntroPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 10px rgba(0, 212, 255, 0.3); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 107, 179, 0.5); }
}

.wechat-footer-note {
    text-align: center;
    font-size: 14px;
    color: #999;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e5e5e5;
}

.wechat-brand-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.wechat-brand-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
    animation: wechatShimmer 3s infinite;
}

@keyframes wechatShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.wechat-brand-name {
    font-size: 24px;
    font-weight: bold;
    color: #ff6bb3;
    text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
}

.wechat-brand-subtitle {
    font-size: 14px;
    color: #00d4ff;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    position: relative;
    z-index: 1;
}

/* WeChat specific responsive optimizations */
@media screen and (max-width: 414px) {
    .wechat-article-container {
        padding: 16px 12px;
    }
    .wechat-article-title {
        font-size: 20px;
        padding: 16px 8px;
    }
    .wechat-article-content h2 {
        font-size: 18px;
        padding: 10px 12px;
    }
    .wechat-article-content h3 {
        font-size: 16px;
        padding: 6px 12px;
    }
    .wechat-brand-name {
        font-size: 20px;
    }
    /* Reduce motion on mobile if user prefers */
    @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}

/* Ensure images within the article content use relative paths if they are in public/img/YYYY-MM-DD/ */
.wechat-article-content img {
    /* Assuming images are stored like /img/2025-05-25/image.png and served from /public */
    /* No specific CSS needed here for paths if using relative URLs correctly in the TSX */
} 