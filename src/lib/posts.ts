// Mock posts library - replaces file system operations with static data
// This prevents "fs module not found" errors in client-side code

export interface PostData {
  slug: string;
  title: string;
  date: string;
  content: string;
  excerpt?: string;
  coverImage?: string;
  tags?: string[];
  category?: string;
  [key: string]: string | string[] | undefined;
}

// Mock data for posts - replace with real data source later
const mockPosts: PostData[] = [
  {
    slug: '2025-05-26',
    title: 'Cursor AI 编辑器深度体验',
    date: '2025-05-26',
    excerpt: '探索 Cursor AI 编辑器的强大功能，从代码补全到智能重构，体验未来编程的新方式。',
    content: `# Cursor AI 编辑器深度体验

Cursor 是一款基于 AI 的代码编辑器，它将人工智能深度集成到编程工作流中...

## 主要功能

- 智能代码补全
- AI 驱动的代码重构
- 自然语言编程
- 实时错误检测

## 使用体验

经过一周的深度使用，Cursor 在以下方面表现出色...`,
    tags: ['AI', '编程工具', 'Cursor'],
    category: '工具评测'
  },
  {
    slug: '2025-05-30',
    title: '未来科技趋势预测',
    date: '2025-05-30',
    excerpt: '分析2025年最重要的科技趋势，从人工智能到量子计算，探索技术发展的未来方向。',
    content: `# 未来科技趋势预测

2025年科技发展呈现出前所未有的加速态势...

## 人工智能的突破

- 大语言模型的进化
- 多模态AI的普及
- AI Agent的兴起

## 量子计算的进展

量子计算正在从实验室走向实际应用...`,
    tags: ['科技趋势', 'AI', '量子计算'],
    category: '技术洞察'
  },
  {
    slug: '2025-06-01',
    title: '整博明美学在现代设计中的应用',
    date: '2025-06-01',
    excerpt: '探讨整博明美学理念如何影响现代UI/UX设计，以及如何在数字产品中实现美学与功能的完美结合。',
    content: `# 整博明美学在现代设计中的应用

整博明美学强调简洁、功能性和美感的统一...

## 设计原则

- 简洁至上
- 功能导向
- 美感融合

## 实际应用

在现代数字产品设计中，整博明美学体现在...`,
    tags: ['设计', '美学', 'UI/UX'],
    category: '设计思考'
  }
];

export function getPostSlugs(): string[] {
  return mockPosts.map(post => post.slug);
}

export function getPostBySlug(dateSlug: string): PostData | null {
  const post = mockPosts.find(p => p.slug === dateSlug);
  return post || null;
}

export function getAllPosts(): PostData[] {
  return mockPosts.sort((a, b) => {
    const dateComparison = b.date.localeCompare(a.date);
    if (dateComparison !== 0) return dateComparison;
    return (a.title || 'zz').localeCompare(b.title || 'zz');
  });
}

// Mock functions for post creation/updating - not functional in this version
export function createPost(dateSlug: string, articleTitle: string, content: string = ''): boolean {
  console.warn('createPost is not implemented in mock version');
  return false;
}

export function updatePost(dateSlug: string, articleFileName: string, newTitle: string, newContent: string, metadata: Record<string, string> = {}): boolean {
  console.warn('updatePost is not implemented in mock version');
  return false;
}