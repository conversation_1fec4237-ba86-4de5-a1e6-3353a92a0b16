import { createTheme, MantineColorScheme } from '@mantine/core';

// 赛博朋克色彩定义
export const cyberpunkColors = {
  cyan: [
    '#cffafe',
    '#a5f3fc', 
    '#67e8f9',
    '#22d3ee',
    '#06b6d4', // 主色调
    '#0891b2',
    '#0e7490',
    '#155e75',
    '#164e63',
    '#083344',
  ],
  pink: [
    '#fdf2f8',
    '#fce7f3',
    '#fbcfe8',
    '#f9a8d4',
    '#f472b6',
    '#ec4899', // 主色调
    '#db2777',
    '#be185d',
    '#9d174d',
    '#831843',
  ],
  purple: [
    '#faf5ff',
    '#f3e8ff',
    '#e9d5ff',
    '#d8b4fe',
    '#c084fc',
    '#a855f7',
    '#9333ea',
    '#7c3aed',
    '#6d28d9',
    '#5b21b6',
  ],
  dark: [
    '#C1C2C5',
    '#A6A7AB',
    '#909296',
    '#5c5f66',
    '#373A40',
    '#2C2E33',
    '#25262b',
    '#1A1B1E',
    '#141517',
    '#101113',
  ],
};

// 深色主题配置
export const darkTheme = createTheme({
  primaryColor: 'cyan',
  fontFamily: 'Arial, Helvetica, sans-serif',
  colors: cyberpunkColors,
  defaultColorScheme: 'dark',
  components: {
    Button: {
      styles: {
        root: {
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    Card: {
      styles: {
        root: {
          backgroundColor: 'rgba(15, 23, 42, 0.8)',
          borderColor: 'rgba(71, 85, 105, 0.3)',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'rgba(6, 182, 212, 0.3)',
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(6, 182, 212, 0.15)',
          },
        },
      },
    },
    Paper: {
      styles: {
        root: {
          backgroundColor: 'rgba(15, 23, 42, 0.8)',
          borderColor: 'rgba(71, 85, 105, 0.3)',
          backdropFilter: 'blur(10px)',
        },
      },
    },
  },
});

// 明亮主题配置
export const lightTheme = createTheme({
  primaryColor: 'cyan',
  fontFamily: 'Arial, Helvetica, sans-serif',
  colors: cyberpunkColors,
  defaultColorScheme: 'light',
  components: {
    Button: {
      styles: {
        root: {
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    Card: {
      styles: {
        root: {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: 'rgba(6, 182, 212, 0.2)',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'rgba(6, 182, 212, 0.4)',
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(6, 182, 212, 0.15)',
          },
        },
      },
    },
    Paper: {
      styles: {
        root: {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: 'rgba(6, 182, 212, 0.2)',
          backdropFilter: 'blur(10px)',
        },
      },
    },
  },
});

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'auto';

// 主题工具函数
export const getSystemColorScheme = (): MantineColorScheme => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'dark';
};

export const getThemeConfig = (mode: ThemeMode) => {
  switch (mode) {
    case 'light':
      return lightTheme;
    case 'dark':
      return darkTheme;
    case 'auto':
      return getSystemColorScheme() === 'dark' ? darkTheme : lightTheme;
    default:
      return darkTheme;
  }
};
